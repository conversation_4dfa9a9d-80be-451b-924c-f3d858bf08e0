# -*- coding: utf-8 -*-
"""
多模态过程监测（三种 mode）数据生成 + Transformer(Multi-Head Attention) 自编码器故障检测
作者：ChatGPT
说明：
  - 按论文截屏里的(34)(35)式生成数据：
        x = A @ [s1, s2]^T + e,  e_i ~ N(0, 0.01)
        Mode1: s1 ~ U(-10,-7),  s2 ~ N(-5,1)
        Mode2: s1 ~ U(-3,-1),   s2 ~ N( 2,1)
        Mode3: s1 ~ U( 2, 5),   s2 ~ N( 7,1)
  - 训练集（正常）：每个 mode 各 200 条（总 600）
  - 监测集 Case1（400 条）：
        1..200:  Mode1 正常
      201..400:  先采样 Mode2 正常后，对 x1 全部 +0.08（阶跃故障）
  - 监测集 Case2（600 条）：
        1..200:   Mode1 正常
      201..400:   Mode2 正常，其中 t∈[301,400] 对 x1 加斜坡 0.002*(t-100)
      401..600:   Mode3 正常，其中 501..600 对 x1 再 +0.08（阶跃故障）
  - 模型：Transformer Encoder（多头注意力）做序列自编码重构，重构误差作监测统计量
  - 阈值：训练窗口误差的 99 分位
"""
import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

import math
import random
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader

# ---------------------
# 0. 复现实验的随机性控制 & 设备选择
# ---------------------
SEED = 42
random.seed(SEED)
np.random.seed(SEED)
torch.manual_seed(SEED)
torch.cuda.manual_seed_all(SEED)
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Device:", DEVICE)

# ---------------------
# 1. 数据生成：三种 mode + 两个监测场景
# ---------------------
A = np.array([
    [0.5768, 0.3766],
    [0.7382, 0.0566],
    [0.8291, 0.4009],
    [0.6519, 0.2070],
    [0.3972, 0.8045]
], dtype=np.float64)  # 5x2 混合矩阵

def sample_mode(n, mode_id, noise_std=0.1):
    """
    生成某个 mode 下的 n 个 5维样本
    参数:
        n: 样本数
        mode_id: 1/2/3
        noise_std: 噪声标准差，对应 N(0,0.01) -> std=0.1
    返回:
        X: [n, 5]
    """
    if mode_id == 1:
        s1 = np.random.uniform(-10, -7, size=(n, 1))
        s2 = np.random.normal(loc=-5, scale=1.0, size=(n, 1))
    elif mode_id == 2:
        s1 = np.random.uniform(-3, -1, size=(n, 1))
        s2 = np.random.normal(loc=2.0, scale=1.0, size=(n, 1))
    elif mode_id == 3:
        s1 = np.random.uniform(2, 5, size=(n, 1))
        s2 = np.random.normal(loc=7.0, scale=1.0, size=(n, 1))
    else:
        raise ValueError("mode_id must be 1/2/3")

    S = np.concatenate([s1, s2], axis=1)  # [n,2]
    X = (A @ S.T).T  # [n,5]
    E = np.random.normal(loc=0.0, scale=noise_std, size=X.shape)
    return X + E

# 正常训练数据：每个 mode 200 条（独立同分布样本）
X_tr_m1 = sample_mode(200, 1)
X_tr_m2 = sample_mode(200, 2)
X_tr_m3 = sample_mode(200, 3)
X_train_all = np.vstack([X_tr_m1, X_tr_m2, X_tr_m3])  # [600, 5]

# 监测用例 Case 1（400）
case1_part1 = sample_mode(200, 1)  # 正常
case1_part2 = sample_mode(200, 2)  # 先正常，再在 x1 上加 0.08 阶跃
case1_part2[:, 0] += 0.08
X_case1 = np.vstack([case1_part1, case1_part2])
y_case1 = np.zeros(400, dtype=np.int64)
y_case1[200:] = 1  # 201..400 为异常（阶跃）

# 监测用例 Case 2（600）
case2_m1 = sample_mode(200, 1)  # 正常
case2_m2 = sample_mode(200, 2)  # 正常
case2_m3 = sample_mode(200, 3)  # 正常

# 斜坡：t in [301..400] 对应全序列索引，落在第二段(201..400)的后半段
# 下面用整个 case2 拼接后的索引计算，先拼接再加故障
X_case2 = np.vstack([case2_m1, case2_m2, case2_m3])  # [600,5]
# 斜坡 0.002*(t-100) 加到 x1, t从1开始
for t in range(301, 401):
    X_case2[t-1, 0] += 0.002 * (t - 100)
# 阶跃：501..600 的 x1 +0.08
X_case2[500:600, 0] += 0.08

# 构建真值标签
y_case2 = np.zeros(600, dtype=np.int64)
y_case2[300:400] = 1  # 301..400 斜坡异常
y_case2[500:600] = 1  # 501..600 阶跃异常

# ---------------------
# 2. 标准化（Z-Score）：用训练正常数据的均值方差
# ---------------------
mu = X_train_all.mean(axis=0, keepdims=True)
std = X_train_all.std(axis=0, keepdims=True) + 1e-8
X_train_all_std = (X_train_all - mu) / std
X_case1_std     = (X_case1     - mu) / std
X_case2_std     = (X_case2     - mu) / std

# ---------------------
# 3. 构造序列样本（滑动窗口）
#    - 仅用“训练集正常数据”拼接成一条序列，然后滑窗切片做自编码训练
#    - 监测集同理，滑窗后用“窗口最后一个时刻”的误差作为该时刻的检测统计量
# ---------------------
SEQ_LEN = 20   # 窗口长度（可调）
STRIDE  = 1    # 步长

def build_windows(series, win_len=20, stride=1):
    """
    从二维数组 series: [T, D] 构造 [N_win, win_len, D] 的滑动窗口张量
    返回：
        Xwin: np.ndarray [N, win_len, D]
        idx_last: 每个窗口最后一个时刻在原序列中的索引（用于对齐检测时刻）
    """
    T, D = series.shape
    xs, idx = [], []
    t = 0
    while t + win_len <= T:
        xs.append(series[t:t+win_len, :])
        idx.append(t + win_len - 1)
        t += stride
    return np.stack(xs, axis=0), np.array(idx, dtype=np.int64)

# 训练序列：把三个 mode 的正常数据串起来（顺序无所谓，也可随机打乱再串）
train_series = X_train_all_std  # [600,5]
Xtr_win, _ = build_windows(train_series, SEQ_LEN, STRIDE)  # [Ntr, L, 5]

# 监测序列窗口
Xc1_win, c1_last_idx = build_windows(X_case1_std, SEQ_LEN, STRIDE)
Xc2_win, c2_last_idx = build_windows(X_case2_std, SEQ_LEN, STRIDE)

# 监测真值标签按“窗口最后一个时刻”对齐
y_case1_aligned = y_case1[c1_last_idx]
y_case2_aligned = y_case2[c2_last_idx]

# ---------------------
# 4. PyTorch 数据集
# ---------------------
class SeqDataset(Dataset):
    def __init__(self, x_win):
        self.x = torch.from_numpy(x_win).float()
    def __len__(self):
        return self.x.shape[0]
    def __getitem__(self, idx):
        # 自编码器任务：输入=输出
        return self.x[idx], self.x[idx]

tr_ds = SeqDataset(Xtr_win)
tr_loader = DataLoader(tr_ds, batch_size=64, shuffle=True, drop_last=True)

# ---------------------
# 5. 模型：Transformer 自编码器（仅 Encoder + 线性重构）
#    - 输入维度 D=5 -> 线性投影到 d_model
#    - 加可学习或固定位置编码，这里用正余弦位置编码
#    - 通过 TransformerEncoder（多头注意力）
#    - 输出再线性映射回 D=5，MSE 作为重构损失
# ---------------------
class PositionalEncoding(nn.Module):
    """
    标准正余弦位置编码，见 'Attention Is All You Need'
    x shape: [B, L, d_model]
    """
    def __init__(self, d_model, max_len=1000):
        super().__init__()
        pe = torch.zeros(max_len, d_model)  # [max_len, d_model]
        position = torch.arange(0, max_len, dtype=torch.float32).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2, dtype=torch.float32)
                             * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)  # 偶数维
        pe[:, 1::2] = torch.cos(position * div_term)  # 奇数维
        self.register_buffer('pe', pe.unsqueeze(0))  # [1, max_len, d_model]

    def forward(self, x):
        L = x.size(1)
        return x + self.pe[:, :L, :]

class TransformerAutoEncoder(nn.Module):
    def __init__(self, feature_dim=5, d_model=64, nhead=4, num_layers=3, dim_ff=128, dropout=0.1):
        super().__init__()
        self.in_proj  = nn.Linear(feature_dim, d_model)
        self.pos_enc  = PositionalEncoding(d_model)
        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead,
                                                   dim_feedforward=dim_ff, dropout=dropout,
                                                   batch_first=True, activation='gelu')
        self.encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.out_proj = nn.Linear(d_model, feature_dim)

    def forward(self, x):
        """
        x: [B, L, D]
        return: 重构的 x_hat [B, L, D]
        """
        h = self.in_proj(x)          # [B, L, d_model]
        h = self.pos_enc(h)          # 加位置编码
        z = self.encoder(h)          # [B, L, d_model]
        x_hat = self.out_proj(z)     # [B, L, D]
        return x_hat

# 实例化与训练配置
model = TransformerAutoEncoder(feature_dim=5, d_model=64, nhead=4, num_layers=3, dim_ff=128, dropout=0.1).to(DEVICE)
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
criterion = nn.MSELoss()

EPOCHS = 40
print("Train samples(windows):", len(tr_ds))

for ep in range(1, EPOCHS+1):
    model.train()
    running = 0.0
    for xb, yb in tr_loader:
        xb = xb.to(DEVICE)  # [B,L,5]
        yb = yb.to(DEVICE)
        y_pred = model(xb)
        loss = criterion(y_pred, yb)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        running += loss.item() * xb.size(0)
    print(f"Epoch {ep:02d}/{EPOCHS} | train MSE={running/len(tr_ds):.6f}")

# ---------------------
# 6. 计算训练集窗口误差分布 -> 设定检测阈值（例如 99% 分位）
# ---------------------
@torch.no_grad()
def window_mse(model, x_win):
    """
    计算每个窗口的重构 MSE（对窗口内所有时刻、所有变量求均值）
    x_win: [N, L, D] numpy
    return: [N] numpy
    """
    model.eval()
    xb = torch.from_numpy(x_win).float().to(DEVICE)
    y_hat = model(xb).cpu().numpy()
    err = ((x_win - y_hat)**2).mean(axis=(1,2))
    return err

tr_err = window_mse(model, Xtr_win)
thresh = np.quantile(tr_err, 0.99)  # 99 分位
print(f"Detection threshold (99% quantile over train windows) = {thresh:.6f}")

# ---------------------
# 7. 在 Case1/Case2 上评估
#    - 以窗口误差作为统计量，映射到“窗口最后一个时刻”的时刻点上
#    - 与真值标签对比，给出指标与可视化
# ---------------------
def evaluate_case(x_win, idx_last, y_aligned, case_name):
    err = window_mse(model, x_win)
    y_pred = (err > thresh).astype(np.int64)

    # 计算指标
    tp = int(((y_pred == 1) & (y_aligned == 1)).sum())
    fp = int(((y_pred == 1) & (y_aligned == 0)).sum())
    tn = int(((y_pred == 0) & (y_aligned == 0)).sum())
    fn = int(((y_pred == 0) & (y_aligned == 1)).sum())
    prec = tp / (tp + fp + 1e-8)
    rec  = tp / (tp + fn + 1e-8)
    f1   = 2*prec*rec / (prec + rec + 1e-8)
    acc  = (tp + tn) / (tp + tn + fp + fn + 1e-8)

    print(f"\n[{case_name}] results:")
    print(f"  Acc={acc:.4f}  Prec={prec:.4f}  Rec={rec:.4f}  F1={f1:.4f}")
    print(f"  TP={tp}  FP={fp}  TN={tn}  FN={fn}")

    # 可视化：误差 & 阈值 & 标签
    # 将标签扩展到同一坐标（0/1），为了叠加显示，把标签乘以一个系数（例如阈值的一半）方便观察
    scale = max(err.max(), thresh) * 0.6
    plt.figure(figsize=(12, 4))
    plt.plot(idx_last+1, err, lw=1.0, label="window MSE")
    plt.hlines(thresh, xmin=(idx_last[0]+1), xmax=(idx_last[-1]+1), linestyles='--', label="threshold")
    plt.plot(idx_last+1, y_aligned * scale, lw=1.0, label="ground-truth (scaled)")
    plt.title(f"{case_name}: Window MSE vs Threshold (aligned to last time of each window)")
    plt.xlabel("time index t")
    plt.ylabel("MSE / scaled label")
    plt.legend()
    plt.tight_layout()
    plt.show()

    return err, y_pred

err1, y1 = evaluate_case(Xc1_win, c1_last_idx, y_case1_aligned, "Case 1")
err2, y2 = evaluate_case(Xc2_win, c2_last_idx, y_case2_aligned, "Case 2")
